<template>
  <UCard
    :ui="{
      root: 'rounded-lg overflow-visible',
      body: 'min-h-[150px] p-0 md:p-0 pl-4',
      footer: 'p-2 md:p-2',
      header: 'sticky -top-6 z-10 bg-[var(--ui-bg)]'
    }"
    :class="
      error ? 'ring-1 ring-red-500 dark:ring-red-400' : 'ring-1 ring-gray-200 dark:ring-gray-800'
    "
  >
    <template #header>
      <div v-if="editor" class="flex items-center justify-between gap-2">
        <div class="flex items-center gap-2">
          <UButton
            size="xs"
            color="neutral"
            :variant="editor.isActive('bold') ? 'solid' : `outline`"
            icon="i-heroicons-bold"
            :disabled="!editor.can().chain().focus().toggleBold().run()"
            @click="editor.chain().focus().toggleBold().run()"
          />
          <UButton
            size="xs"
            color="neutral"
            :variant="editor.isActive('italic') ? 'solid' : `outline`"
            icon="i-lucide-italic"
            square
            :disabled="!editor.can().chain().focus().toggleItalic().run()"
            @click="editor.chain().focus().toggleItalic().run()"
          />
          <UButton
            size="xs"
            color="neutral"
            :variant="editor.isActive('strike') ? 'solid' : `outline`"
            icon="i-lucide-strikethrough"
            square
            :disabled="!editor.can().chain().focus().toggleStrike().run()"
            @click="editor.chain().focus().toggleStrike().run()"
          />
          <UButton
            size="xs"
            icon="i-lucide-list"
            square
            color="neutral"
            :variant="editor.isActive('bulletList') ? 'solid' : `outline`"
            @click="editor.chain().focus().toggleBulletList().run()"
          />
          <UButton
            size="xs"
            icon="i-lucide-list"
            square
            color="neutral"
            :variant="editor.isActive('orderedList') ? 'solid' : `outline`"
            @click="editor.chain().focus().toggleOrderedList().run()"
          />
          <UButton
            size="xs"
            color="neutral"
            variant="outline"
            icon="i-lucide-undo"
            square
            :disabled="!editor.can().chain().focus().undo().run()"
            @click="editor.chain().focus().undo().run()"
          />
          <UButton
            size="xs"
            color="neutral"
            variant="outline"
            icon="i-lucide-redo"
            square
            :disabled="!editor.can().chain().focus().redo().run()"
            @click="editor.chain().focus().redo().run()"
          />
          <UButton
            size="xs"
            color="neutral"
            variant="outline"
            icon="i-lucide-image"
            square
            @click="addImage"
          />
          <UButton
            size="xs"
            color="neutral"
            variant="outline"
            icon="i-lucide-youtube"
            square
            @click="addVideo"
          />
          <drag-handle :editor="editor">
            <div class="custom-drag-handle" />
          </drag-handle>
        </div>
        <div class="text-xs">
          {{ editor.storage.characterCount.characters() }} символов |
          {{ editor.storage.characterCount.words() }} слов
        </div>
      </div>
      <div v-else class="flex items-center gap-2">
        <USkeleton v-for="i in 7" :key="i" class="h-6 w-6 rounded-md" />
      </div>
    </template>
    <TiptapEditorContent
      class="prose dark:prose-invert prose-ul:my-1 prose-ol:my-1 prose-li:my-1 prose-p:my-1 prose-headings:my-1 prose-h1:mb-4 max-w-none focus:outline-none"
      :editor="editor"
      :placeholder="placeholder"
    />
    <USkeleton v-if="!editor" class="h-[150px] w-full" />
  </UCard>
  <pre>{{ modelValue }}</pre>
</template>

<script setup lang="ts">
import { Youtube as TiptapYoutube } from "@tiptap/extension-youtube";
import { Image as TiptapImage } from "@tiptap/extension-image";
import { CharacterCount as TiptapCharacterCount } from "@tiptap/extension-character-count";
import { DragHandle } from '@tiptap/extension-drag-handle-vue-3'
import NodeRange from '@tiptap/extension-node-range'

const props = defineProps({
  modelValue: {
    type: String,
    default: ""
  },
  placeholder: {
    type: String,
    default: ""
  },
  error: {
    type: String,
    default: ""
  }
});
const emit = defineEmits(["update:modelValue"]);

const editor = useEditor({
  content: props.modelValue,
  extensions: [TiptapStarterKit, TiptapYoutube, TiptapImage, TiptapCharacterCount, NodeRange.configure({
    // allow to select only on depth 0
    // depth: 0,
    key: null,
  })],
  onUpdate({ editor }) {
    emit("update:modelValue", editor.getJSON());
  }
});

const addVideo = () => {
  const url = prompt("Enter YouTube URL");

  editor.value.commands.setYoutubeVideo({
    src: url
  });
};
const addImage = () => {
  const url = prompt("Enter image URL");

  if (url) {
    editor.value.chain().focus().setImage({ src: url }).run();
  }
};
</script>

<style>
.ProseMirror {
  padding: 0.5rem;
  border: 2px solid transparent;
  border-radius: 0.375rem;
  min-height: 150px;
}
.ProseMirror-focused {
  outline: none !important;
  border-color: rgb(var(--color-primary-400) / var(--tw-ring-opacity));
}
::selection {
  background-color: #70cff850;
}
.ProseMirror-selectednode,
.ProseMirror-selectednoderange {
  position: relative;
}
.ProseMirror-selectednode::before,
.ProseMirror-selectednoderange::before {
  position: absolute;
  pointer-events: none;
  z-index: -1;
  content: '';
  top: -0.25rem;
  left: -0.25rem;
  right: -0.25rem;
  bottom: -0.25rem;
  background-color: #70cff850;
  border-radius: 0.2rem;
}
.custom-drag-handle::after {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1rem;
    height: 1.25rem;
    content: '⠿';
    font-weight: 700;
    cursor: grab;
    background: #0d0d0d10;
    color: #0d0d0d50;
    border-radius: 0.25rem;
}
</style>
